name: Build and Push Docker Image

on:
  push:
    branches: [ "main" ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Set up Git user
      run: |
        git config --global user.name "github-actions[bot]"
        git config --global user.email "github-actions[bot]@users.noreply.github.com"

    - name: Fetch all tags
      run: git fetch --tags

    - name: Get latest tag
      id: get_tag
      run: |
        tag=$(git tag --sort=-v:refname | head -n 1)
        echo "latest_tag=$tag" >> $GITHUB_OUTPUT

    - name: Calculate new tag
      id: new_tag
      run: |
        latest=${{ steps.get_tag.outputs.latest_tag }}
        if [[ $latest =~ ^v([0-9]+)\.([0-9]+)\.([0-9]+)$ ]]; then
          major=${BASH_REMATCH[1]}
          minor=${BASH_REMATCH[2]}
          patch=${BASH_REMATCH[3]}
          patch=$((patch + 1))
          new_tag="v$major.$minor.$patch"
        else
          new_tag="v1.0.0"
        fi
        echo "new_tag=$new_tag" >> $GITHUB_OUTPUT

    - name: Create and push new tag
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        git tag ${{ steps.new_tag.outputs.new_tag }}
        git push origin ${{ steps.new_tag.outputs.new_tag }}

    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: |
          fz19870823190/telegram-115bot:latest
          fz19870823190/telegram-115bot:${{ steps.new_tag.outputs.new_tag }}